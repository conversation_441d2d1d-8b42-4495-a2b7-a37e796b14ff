import { useState } from "react";
import { X, MoreHorizontal } from "lucide-react";

import type {
  EventsModule as EventsModuleType,
  LiveClass,
} from "~/lib/api/types";
import { useRegisterLiveClass, useMyGroups } from "~/lib/api/client-queries";

interface EventsModuleProps {
  module: EventsModuleType;
  liveClasses?: LiveClass[];
  params: {
    groupId: string;
    cohortId: string;
    moduleId: string;
  };
}

interface EventDetailsModalProps {
  event: LiveClass;
  isOpen: boolean;
  onClose: () => void;
  onRSVP: (eventId: number) => void;
  isUpcoming: boolean;
  isRegistering: boolean;
}

function EventDetailsModal({
  event,
  isOpen,
  onClose,
  onRSVP,
  isUpcoming,
  isRegistering,
}: EventDetailsModalProps) {
  if (!isOpen) return null;

  const startTime = new Date(event.startAt);
  const endTime = new Date(event.endsAt);
  const timeString = `${startTime.toLocaleString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  })} - ${endTime.toLocaleString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  })}`;

  const dateString = startTime.toLocaleString("en-US", {
    weekday: "short",
    month: "short",
    day: "numeric",
  });

  // Calculate duration
  const durationMs = endTime.getTime() - startTime.getTime();
  const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
  const durationMinutes = Math.floor(
    (durationMs % (1000 * 60 * 60)) / (1000 * 60)
  );
  const durationString =
    durationHours > 0
      ? `${durationHours} hr ${
          durationMinutes > 0 ? durationMinutes + " min" : ""
        }`
      : `${durationMinutes} min`;

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-zinc-900 rounded-lg p-6 max-w-md w-full mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Join Event</h2>
          <button
            onClick={onClose}
            className="text-zinc-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <h3 className="text-2xl font-semibold text-white mb-6">
          {event.topic}
        </h3>

        <div className="space-y-4 mb-6">
          <div className="flex justify-between">
            <span className="text-zinc-400">Time</span>
            <span className="text-white">{timeString}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-zinc-400">Date</span>
            <span className="text-white">{dateString}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-zinc-400">Duration</span>
            <span className="text-white">{durationString}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-zinc-400">Host by</span>
            <span className="text-white">Slo Studio</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-zinc-400">Participants</span>
            <div className="flex items-center gap-2">
              <div className="flex -space-x-2">
                {[...Array(Math.min(3, event.numOfParticipants))].map(
                  (_, i) => (
                    <div
                      key={i}
                      className="w-6 h-6 rounded-full bg-zinc-600 border-2 border-zinc-900"
                    />
                  )
                )}
              </div>
              <span className="text-white text-sm">
                {event.numOfParticipants} Attending
              </span>
            </div>
          </div>
        </div>

        {isUpcoming && !event.isRegistered && (
          <button
            onClick={() => onRSVP(event.id)}
            disabled={isRegistering}
            className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-600/50 text-white font-medium py-3 rounded-lg transition-colors disabled:cursor-not-allowed"
          >
            {isRegistering ? "Registering..." : "RSVP"}
          </button>
        )}
        {event.isRegistered && (
          <div className="text-center text-green-400 font-medium">
            ✓ You're registered for this event
          </div>
        )}
        {!isUpcoming && !event.isRegistered && (
          <div className="text-center text-zinc-400">
            This event has already passed
          </div>
        )}
      </div>
    </div>
  );
}

export default function EventsModule({
  module,
  liveClasses = [],
  params,
}: EventsModuleProps) {
  const [activeTab, setActiveTab] = useState<"upcoming" | "past">("upcoming");
  const [selectedEvent, setSelectedEvent] = useState<LiveClass | null>(null);
  const registerLiveClass = useRegisterLiveClass();

  // Fetch group data to get banner image
  const { data: myGroupsResponse } = useMyGroups();
  const group = myGroupsResponse?.groups.byId[params.groupId];

  const now = new Date();
  const upcomingEvents = liveClasses.filter(
    (event) => new Date(event.startAt) >= now
  );
  const pastEvents = liveClasses.filter(
    (event) => new Date(event.startAt) < now
  );

  const currentEvents = activeTab === "upcoming" ? upcomingEvents : pastEvents;

  // Group events by month
  const groupedEvents = currentEvents.reduce((acc, event) => {
    const date = new Date(event.startAt);
    const monthKey = date.toLocaleString("en-US", {
      month: "long",
      year: "numeric",
    });

    if (!acc[monthKey]) {
      acc[monthKey] = [];
    }
    acc[monthKey].push(event);
    return acc;
  }, {} as Record<string, LiveClass[]>);

  // Sort months and events within each month
  const sortedMonths = Object.keys(groupedEvents).sort((a, b) => {
    const dateA = new Date(a);
    const dateB = new Date(b);
    return activeTab === "upcoming"
      ? dateA.getTime() - dateB.getTime()
      : dateB.getTime() - dateA.getTime();
  });

  const getTimeUntilStart = (startTime: Date) => {
    const diffInMinutes = Math.floor(
      (startTime.getTime() - now.getTime()) / (1000 * 60)
    );
    if (diffInMinutes <= 60 && diffInMinutes > 0) {
      return `Starting in ${diffInMinutes} mins`;
    }
    return null;
  };

  const handleRSVP = async (eventId: number) => {
    try {
      await registerLiveClass.mutateAsync({
        groupId: params.groupId,
        cohortId: params.cohortId,
        moduleId: params.moduleId,
        liveClassId: eventId.toString(),
      });
      setSelectedEvent(null);
    } catch (error) {
      console.error("Failed to register for event:", error);
    }
  };

  return (
    <div className="bg-black min-h-screen">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-800">
        <div className="max-w-4xl mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-white">
                {module.name}
              </h1>
              <p className="text-sm text-zinc-400">
                {currentEvents.length} Events
              </p>
            </div>
            <button className="p-2 hover:bg-zinc-800 rounded-lg transition-colors">
              <MoreHorizontal className="w-5 h-5 text-zinc-400" />
            </button>
          </div>
        </div>
      </div>

      {/* Header with banner */}
      <div className="relative h-64 bg-gray-900">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: group?.bannerImage
              ? `url(${group.bannerImage})`
              : `linear-gradient(135deg, #1e293b 0%, #0f172a 100%)`,
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-gray-950 via-gray-950/80 to-transparent" />
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-8 py-12">
        {/* Tabs */}
        <div className="flex gap-2 mb-8">
          {(["upcoming", "past"] as const).map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-1.5 rounded-full text-sm font-medium transition-colors capitalize ${
                activeTab === tab
                  ? "bg-white text-black"
                  : "bg-zinc-800 text-zinc-400 hover:text-zinc-300"
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Events List */}
        {sortedMonths.length === 0 ? (
          <div className="text-center py-16">
            <p className="text-zinc-400 text-lg">
              No {activeTab} events at the moment.
            </p>
          </div>
        ) : (
          <div className="space-y-12">
            {sortedMonths.map((monthYear) => {
              const events = groupedEvents[monthYear];
              const firstEventDate = new Date(events[0].startAt);
              const monthName = firstEventDate.toLocaleString("en-US", {
                month: "long",
              });

              return (
                <div key={monthYear}>
                  <h3 className="text-2xl font-semibold text-white mb-6">
                    {monthName}
                  </h3>

                  <div className="space-y-3">
                    {events.map((event) => {
                      const startTime = new Date(event.startAt);
                      const endTime = new Date(event.endsAt);
                      const timeString = `${startTime.toLocaleString("en-US", {
                        hour: "numeric",
                        minute: "2-digit",
                        hour12: true,
                      })} - ${endTime.toLocaleString("en-US", {
                        hour: "numeric",
                        minute: "2-digit",
                        hour12: true,
                      })}`;
                      const dateString = startTime.toLocaleString("en-US", {
                        day: "numeric",
                        month: "short",
                      });
                      const startingSoon =
                        activeTab === "upcoming"
                          ? getTimeUntilStart(startTime)
                          : null;

                      return (
                        <button
                          key={event.id}
                          onClick={() => setSelectedEvent(event)}
                          className="w-full bg-zinc-800/50 hover:bg-zinc-800 rounded-lg p-4 transition-colors text-left"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-4 mb-2">
                                <span className="text-zinc-400 text-sm">
                                  {timeString}
                                </span>
                                {startingSoon && (
                                  <span className="text-green-400 text-sm font-medium">
                                    {startingSoon}
                                  </span>
                                )}
                                {event.isRegistered && (
                                  <span className="px-2 py-0.5 bg-green-900/20 text-green-400 text-xs rounded-full border border-green-900/30">
                                    Going
                                  </span>
                                )}
                              </div>
                              <h4 className="text-lg font-medium text-white mb-1">
                                {event.topic}
                              </h4>
                              <p className="text-zinc-400 text-sm mb-3">
                                Weekly Training
                              </p>
                              <div className="flex items-center gap-2">
                                <div className="flex -space-x-2">
                                  {[
                                    ...Array(
                                      Math.min(3, event.numOfParticipants)
                                    ),
                                  ].map((_, i) => (
                                    <div
                                      key={i}
                                      className="w-6 h-6 rounded-full bg-zinc-600 border-2 border-zinc-800"
                                    />
                                  ))}
                                  {event.numOfParticipants > 3 && (
                                    <div className="w-6 h-6 rounded-full bg-zinc-700 border-2 border-zinc-800 flex items-center justify-center">
                                      <span className="text-xs text-zinc-300">
                                        +{event.numOfParticipants - 3}
                                      </span>
                                    </div>
                                  )}
                                </div>
                                <span className="text-zinc-400 text-sm">
                                  {event.numOfParticipants} Participants
                                </span>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-white font-medium">
                                {dateString}
                              </div>
                            </div>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Event Details Modal */}
      <EventDetailsModal
        event={selectedEvent!}
        isOpen={!!selectedEvent}
        onClose={() => setSelectedEvent(null)}
        onRSVP={handleRSVP}
        isUpcoming={activeTab === "upcoming"}
        isRegistering={registerLiveClass.isPending}
      />
    </div>
  );
}
